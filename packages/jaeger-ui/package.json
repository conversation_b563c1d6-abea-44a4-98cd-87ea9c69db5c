{"private": true, "name": "jae<PERSON>-ui", "version": "1.71.0", "main": "src/index.tsx", "license": "Apache-2.0", "homepage": ".", "repository": {"type": "git", "url": "https://github.com/jaegertracing/jaeger-ui.git", "directory": "packages/jaeger-ui"}, "devDependencies": {"@babel/core": "^7.24.6", "@babel/preset-env": "^7.24.6", "@babel/preset-react": "^7.24.6", "@babel/preset-typescript": "^7.24.6", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.6.1", "@types/deep-freeze": "^0.1.1", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.0", "@types/object-hash": "^3.0.2", "@types/react": "^18.3.11", "@types/react-helmet": "^6.1.5", "@types/react-router-dom": "^5.1.0", "@types/react-window": "^1.8.0", "@types/redux-actions": "2.2.1", "@vitejs/plugin-legacy": "^6.0.0", "@vitejs/plugin-react": "^4.3.4", "@wojtekmaj/enzyme-adapter-react-17": "^0.8.0", "babel-jest": "^29.5.0", "babel-plugin-inline-react-svg": "^2.0.2", "babel-plugin-react-remove-properties": "^0.3.0", "enzyme": "^3.8.0", "enzyme-to-json": "^3.6.2", "jest": "^29.6.2", "jest-environment-jsdom": "^29.5.0", "jest-junit": "^16.0.0", "less": "4.3.0", "react-test-renderer": "^18.3.1", "rollup-plugin-visualizer": "^6.0.0", "sinon": "^21.0.0", "terser": "^5.31.0", "vite": "^6.0.7", "vite-plugin-imp": "^2.4.0"}, "dependencies": {"@ant-design/compatible": "^5.1.4", "@jaegertracing/plexus": "0.2.0", "@pyroscope/flamegraph": "0.21.4", "@sentry/browser": "9.38.0", "antd": "^5.23.3", "chance": "^1.0.10", "classnames": "^2.5.1", "combokeys": "^3.0.0", "copy-to-clipboard": "^3.1.0", "dayjs": "^1.11.9", "deep-freeze": "^0.0.1", "drange": "^2.0.0", "history": "^4.6.3", "isomorphic-fetch": "^3.0.0", "lodash": "^4.17.19", "logfmt": "^1.4.0", "lru-memoize": "^1.1.0", "match-sorter": "^8.0.0", "memoize-one": "^6.0.0", "object-hash": "^3.0.0", "prop-types": "^15.5.10", "query-string": "^9.0.0", "react": "^18.3.1", "react-circular-progressbar": "^2.1.0", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-icons": "^5.0.1", "react-is": "^18.2.0", "react-json-view-lite": "2.4.1", "react-redux": "^9.2.0", "react-router-dom": "5.3.4", "react-router-dom-v5-compat": "^6.24.0", "react-window": "^1.8.10", "recharts": "^3.0.0", "redux": "^5.0.1", "redux-actions": "3.0.3", "redux-first-history": "^5.2.0", "redux-promise-middleware": "^6.1.3", "store": "^2.0.12", "ts-key-enum": "^2.0.0", "tween-functions": "^1.2.0", "u-basscss": "2.0.1"}, "scripts": {"build": "NODE_ENV=production REACT_APP_VSN_STATE=$(../../scripts/get-tracking-version.js) vite build", "coverage": "npm run test-ci -- --coverage", "start:ga-debug": "REACT_APP_GA_DEBUG=1 REACT_APP_VSN_STATE=$(../../scripts/get-tracking-version.js) vite", "start": "vite", "test": "jest --maxWorkers=50%", "test-ci": "CI=1 jest --silent --color --maxWorkers=4"}, "jest": {"globalSetup": "./test/jest.global-setup.js", "setupFilesAfterEnv": ["./test/jest-per-test-setup.js"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/setup*.js", "!src/utils/DraggableManager/demo/*.tsx", "!src/utils/test/**/*.js", "!src/demo/**/*.js", "!src/types/*"], "reporters": ["default", "jest-junit"], "transform": {"\\.(css|png)$": "./test/generic-file-transform.js", "\\.([jt]sx?|svg)$": "./test/babel-transform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\](?!redux-actions|query-string|decode-uri-component|filter-obj|split-on-first|sinon|d3-.*).+\\.(js|jsx|mjs|cjs|ts|tsx)$"], "testEnvironment": "jsdom", "snapshotFormat": {"escapeString": true, "printBasicPrototype": true}}}