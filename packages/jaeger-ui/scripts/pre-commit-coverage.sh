#!/bin/bash

# Copyright (c) 2024 The Jaeger Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

set -e

echo "🧪 Running pre-commit coverage validation..."

# Change to the jaeger-ui package directory
cd "$(dirname "$0")/.."

# Check if there are any staged JavaScript/TypeScript files
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(js|jsx|ts|tsx)$' | grep '^src/' || true)

if [ -z "$STAGED_FILES" ]; then
    echo "✅ No JavaScript/TypeScript source files staged for commit."
    exit 0
fi

echo "📁 Found staged source files:"
echo "$STAGED_FILES" | sed 's/^/   - /'
echo

# Run tests with coverage for staged files
echo "🔍 Running tests with coverage..."
npm run coverage > /dev/null 2>&1

if [ $? -ne 0 ]; then
    echo "❌ Tests failed. Please fix failing tests before committing."
    exit 1
fi

# Check coverage for changed files
echo "📊 Validating coverage for changed files..."
npm run coverage:changed

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ Coverage validation failed!"
    echo ""
    echo "💡 To fix this:"
    echo "   1. Add tests for the files that don't meet coverage thresholds"
    echo "   2. Use /* istanbul ignore next */ comments for untestable code"
    echo "   3. Run 'npm run coverage' to see detailed coverage report"
    echo ""
    exit 1
fi

echo ""
echo "✅ All coverage checks passed! Ready to commit."
