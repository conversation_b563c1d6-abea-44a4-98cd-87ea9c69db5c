#!/usr/bin/env node

// Copyright (c) 2024 The Jaeger Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * Script to show coverage diff between current branch and main branch.
 * Helps reviewers understand coverage impact of changes.
 */

function getCoverageData(branch = null) {
  const currentBranch = branch || execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();
  
  try {
    if (branch && branch !== currentBranch) {
      // Stash current changes
      execSync('git stash push -m "coverage-diff-temp"', { stdio: 'ignore' });
      
      // Checkout target branch
      execSync(`git checkout ${branch}`, { stdio: 'ignore' });
      
      // Run coverage
      execSync('npm run coverage', { stdio: 'ignore' });
    }
    
    const coveragePath = path.join(__dirname, '../coverage/coverage-final.json');
    
    if (!fs.existsSync(coveragePath)) {
      return null;
    }
    
    return JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
  } catch (error) {
    return null;
  } finally {
    if (branch && branch !== currentBranch) {
      // Return to original branch
      execSync(`git checkout ${currentBranch}`, { stdio: 'ignore' });
      
      // Restore stashed changes
      try {
        execSync('git stash pop', { stdio: 'ignore' });
      } catch (e) {
        // No stash to pop
      }
    }
  }
}

function calculateFileCoverage(fileCoverage) {
  if (!fileCoverage) return null;
  
  const { s: statements, b: branches, f: functions } = fileCoverage;
  
  const statementsCovered = Object.values(statements).filter(count => count > 0).length;
  const statementsTotal = Object.keys(statements).length;
  
  const branchesCovered = Object.values(branches).flat().filter(count => count > 0).length;
  const branchesTotal = Object.values(branches).flat().length;
  
  const functionsCovered = Object.values(functions).filter(count => count > 0).length;
  const functionsTotal = Object.keys(functions).length;
  
  return {
    statements: statementsTotal > 0 ? (statementsCovered / statementsTotal) * 100 : 100,
    branches: branchesTotal > 0 ? (branchesCovered / branchesTotal) * 100 : 100,
    functions: functionsTotal > 0 ? (functionsCovered / functionsTotal) * 100 : 100,
  };
}

function getChangedFiles() {
  try {
    const output = execSync('git diff --name-only origin/main...HEAD', { encoding: 'utf8' });
    return output
      .split('\n')
      .filter(file => file.trim())
      .filter(file => file.match(/\.([jt]sx?)$/))
      .filter(file => file.startsWith('src/'))
      .filter(file => !file.includes('.test.'))
      .filter(file => !file.includes('.spec.'))
      .filter(file => !file.includes('__tests__'))
      .filter(file => fs.existsSync(file));
  } catch (error) {
    return [];
  }
}

function formatCoverageChange(current, previous, metric) {
  if (previous === null) return `${current.toFixed(1)}% (new)`;
  
  const diff = current - previous;
  const sign = diff > 0 ? '+' : '';
  const color = diff > 0 ? '🟢' : diff < 0 ? '🔴' : '⚪';
  
  return `${current.toFixed(1)}% (${sign}${diff.toFixed(1)}%) ${color}`;
}

function main() {
  console.log('📊 Generating coverage diff report...\n');
  
  // Get current coverage
  console.log('🔍 Analyzing current branch coverage...');
  const currentCoverage = getCoverageData();
  
  if (!currentCoverage) {
    console.error('❌ Could not get current coverage data. Run "npm run coverage" first.');
    process.exit(1);
  }
  
  // Get main branch coverage
  console.log('🔍 Analyzing main branch coverage...');
  const mainCoverage = getCoverageData('origin/main');
  
  const changedFiles = getChangedFiles();
  
  if (changedFiles.length === 0) {
    console.log('✅ No changed source files found.');
    return;
  }
  
  console.log(`\n📁 Coverage diff for ${changedFiles.length} changed file(s):\n`);
  
  changedFiles.forEach(file => {
    const fullPath = path.resolve(file);
    const currentFileCoverage = calculateFileCoverage(currentCoverage[fullPath]);
    const mainFileCoverage = mainCoverage ? calculateFileCoverage(mainCoverage[fullPath]) : null;
    
    console.log(`📄 ${file}:`);
    
    if (!currentFileCoverage) {
      console.log('   ⚠️  No coverage data (file may not be tested)');
      return;
    }
    
    ['statements', 'branches', 'functions'].forEach(metric => {
      const current = currentFileCoverage[metric];
      const previous = mainFileCoverage ? mainFileCoverage[metric] : null;
      const change = formatCoverageChange(current, previous, metric);
      
      console.log(`   ${metric.padEnd(10)}: ${change}`);
    });
    
    console.log();
  });
  
  console.log('💡 Legend: 🟢 Improved, 🔴 Decreased, ⚪ No change, (new) New file');
}

if (require.main === module) {
  main();
}

module.exports = { getCoverageData, calculateFileCoverage, getChangedFiles, formatCoverageChange };
