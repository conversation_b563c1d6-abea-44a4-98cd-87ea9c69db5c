#!/usr/bin/env node

// Copyright (c) 2024 The Jaeger Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * Comprehensive coverage reporting script for developers.
 * Provides detailed coverage analysis and actionable insights.
 */

const COVERAGE_THRESHOLDS = {
  statements: 80,
  branches: 80,
  functions: 80,
  lines: 80,
};

function parseCoverageReport() {
  const coveragePath = path.join(__dirname, '../coverage/coverage-final.json');
  
  if (!fs.existsSync(coveragePath)) {
    console.error('❌ Coverage report not found. Run "npm run coverage" first.');
    process.exit(1);
  }

  return JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
}

function calculateOverallCoverage(coverageData) {
  let totalStatements = 0, coveredStatements = 0;
  let totalBranches = 0, coveredBranches = 0;
  let totalFunctions = 0, coveredFunctions = 0;
  let totalLines = 0, coveredLines = 0;

  Object.values(coverageData).forEach(fileCoverage => {
    const { s: statements, b: branches, f: functions, l: lines } = fileCoverage;
    
    totalStatements += Object.keys(statements).length;
    coveredStatements += Object.values(statements).filter(count => count > 0).length;
    
    totalBranches += Object.values(branches).flat().length;
    coveredBranches += Object.values(branches).flat().filter(count => count > 0).length;
    
    totalFunctions += Object.keys(functions).length;
    coveredFunctions += Object.values(functions).filter(count => count > 0).length;
    
    if (lines) {
      totalLines += Object.keys(lines).length;
      coveredLines += Object.values(lines).filter(count => count > 0).length;
    }
  });

  return {
    statements: totalStatements > 0 ? (coveredStatements / totalStatements) * 100 : 100,
    branches: totalBranches > 0 ? (coveredBranches / totalBranches) * 100 : 100,
    functions: totalFunctions > 0 ? (coveredFunctions / totalFunctions) * 100 : 100,
    lines: totalLines > 0 ? (coveredLines / totalLines) * 100 : 100,
  };
}

function findLowCoverageFiles(coverageData, threshold = 70) {
  const lowCoverageFiles = [];

  Object.entries(coverageData).forEach(([filePath, fileCoverage]) => {
    const { s: statements, b: branches, f: functions } = fileCoverage;
    
    const statementsCovered = Object.values(statements).filter(count => count > 0).length;
    const statementsTotal = Object.keys(statements).length;
    const statementsPercent = statementsTotal > 0 ? (statementsCovered / statementsTotal) * 100 : 100;
    
    const branchesCovered = Object.values(branches).flat().filter(count => count > 0).length;
    const branchesTotal = Object.values(branches).flat().length;
    const branchesPercent = branchesTotal > 0 ? (branchesCovered / branchesTotal) * 100 : 100;
    
    const functionsCovered = Object.values(functions).filter(count => count > 0).length;
    const functionsTotal = Object.keys(functions).length;
    const functionsPercent = functionsTotal > 0 ? (functionsCovered / functionsTotal) * 100 : 100;
    
    const minCoverage = Math.min(statementsPercent, branchesPercent, functionsPercent);
    
    if (minCoverage < threshold) {
      lowCoverageFiles.push({
        file: filePath.replace(process.cwd() + '/', ''),
        statements: statementsPercent,
        branches: branchesPercent,
        functions: functionsPercent,
        minCoverage,
      });
    }
  });

  return lowCoverageFiles.sort((a, b) => a.minCoverage - b.minCoverage);
}

function getUncoveredLines(fileCoverage) {
  const { s: statements } = fileCoverage;
  const uncoveredLines = [];
  
  Object.entries(statements).forEach(([line, count]) => {
    if (count === 0) {
      uncoveredLines.push(parseInt(line));
    }
  });
  
  return uncoveredLines.sort((a, b) => a - b);
}

function formatCoveragePercent(percent, threshold) {
  const emoji = percent >= threshold ? '✅' : percent >= threshold - 10 ? '⚠️' : '❌';
  return `${emoji} ${percent.toFixed(1)}%`;
}

function main() {
  console.log('📊 Jaeger UI Coverage Report\n');
  console.log('=' .repeat(50));
  
  // Run coverage if not exists
  const coveragePath = path.join(__dirname, '../coverage/coverage-final.json');
  if (!fs.existsSync(coveragePath)) {
    console.log('🔍 Running coverage analysis...');
    execSync('npm run coverage', { stdio: 'inherit' });
    console.log();
  }
  
  const coverageData = parseCoverageReport();
  const overall = calculateOverallCoverage(coverageData);
  
  // Overall coverage summary
  console.log('📈 Overall Coverage:');
  console.log(`   Statements: ${formatCoveragePercent(overall.statements, COVERAGE_THRESHOLDS.statements)}`);
  console.log(`   Branches:   ${formatCoveragePercent(overall.branches, COVERAGE_THRESHOLDS.branches)}`);
  console.log(`   Functions:  ${formatCoveragePercent(overall.functions, COVERAGE_THRESHOLDS.functions)}`);
  console.log(`   Lines:      ${formatCoveragePercent(overall.lines, COVERAGE_THRESHOLDS.lines)}`);
  console.log();
  
  // Check if overall thresholds are met
  const thresholdsMet = Object.entries(COVERAGE_THRESHOLDS).every(([metric, threshold]) => 
    overall[metric] >= threshold
  );
  
  if (thresholdsMet) {
    console.log('🎉 All coverage thresholds met!');
  } else {
    console.log('⚠️  Some coverage thresholds not met.');
  }
  console.log();
  
  // Low coverage files
  const lowCoverageFiles = findLowCoverageFiles(coverageData, 70);
  
  if (lowCoverageFiles.length > 0) {
    console.log('🔍 Files needing attention (< 70% coverage):');
    console.log();
    
    lowCoverageFiles.slice(0, 10).forEach(({ file, statements, branches, functions, minCoverage }) => {
      console.log(`📄 ${file} (${minCoverage.toFixed(1)}% min):`);
      console.log(`   Statements: ${statements.toFixed(1)}%`);
      console.log(`   Branches:   ${branches.toFixed(1)}%`);
      console.log(`   Functions:  ${functions.toFixed(1)}%`);
      console.log();
    });
    
    if (lowCoverageFiles.length > 10) {
      console.log(`   ... and ${lowCoverageFiles.length - 10} more files`);
      console.log();
    }
  }
  
  // Actionable recommendations
  console.log('💡 Recommendations:');
  console.log('   • Run "npm run coverage:changed" to check only modified files');
  console.log('   • Run "npm run coverage:diff" to see coverage changes vs main branch');
  console.log('   • Use /* istanbul ignore next */ for untestable code');
  console.log('   • Open coverage/lcov-report/index.html for detailed line-by-line coverage');
  console.log();
  
  console.log('🚀 Happy testing!');
}

if (require.main === module) {
  main();
}

module.exports = { 
  parseCoverageReport, 
  calculateOverallCoverage, 
  findLowCoverageFiles, 
  getUncoveredLines,
  formatCoveragePercent 
};
