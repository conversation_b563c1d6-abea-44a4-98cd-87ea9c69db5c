#!/usr/bin/env node

// Copyright (c) 2024 The Jaeger Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * <PERSON>ript to check code coverage for changed files only.
 * This helps ensure new code meets coverage standards without requiring
 * the entire codebase to meet thresholds immediately.
 */

const COVERAGE_THRESHOLDS = {
  statements: 80,
  branches: 80,
  functions: 80,
  lines: 80,
};

function getChangedFiles() {
  try {
    // Get changed files compared to main branch
    const output = execSync('git diff --name-only origin/main...HEAD', { encoding: 'utf8' });
    return output
      .split('\n')
      .filter(file => file.trim())
      .filter(file => file.match(/\.([jt]sx?)$/))
      .filter(file => file.startsWith('src/'))
      .filter(file => !file.includes('.test.'))
      .filter(file => !file.includes('.spec.'))
      .filter(file => !file.includes('__tests__'))
      .filter(file => fs.existsSync(file));
  } catch (error) {
    console.warn('Could not get changed files, checking all files');
    return [];
  }
}

function parseCoverageReport() {
  const coveragePath = path.join(__dirname, '../coverage/coverage-final.json');
  
  if (!fs.existsSync(coveragePath)) {
    console.error('❌ Coverage report not found. Run "npm run coverage" first.');
    process.exit(1);
  }

  return JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
}

function calculateCoverage(fileCoverage) {
  const { s: statements, b: branches, f: functions } = fileCoverage;
  
  const statementsCovered = Object.values(statements).filter(count => count > 0).length;
  const statementsTotal = Object.keys(statements).length;
  
  const branchesCovered = Object.values(branches).flat().filter(count => count > 0).length;
  const branchesTotal = Object.values(branches).flat().length;
  
  const functionsCovered = Object.values(functions).filter(count => count > 0).length;
  const functionsTotal = Object.keys(functions).length;
  
  return {
    statements: statementsTotal > 0 ? (statementsCovered / statementsTotal) * 100 : 100,
    branches: branchesTotal > 0 ? (branchesCovered / branchesTotal) * 100 : 100,
    functions: functionsTotal > 0 ? (functionsCovered / functionsTotal) * 100 : 100,
    lines: fileCoverage.l ? Object.values(fileCoverage.l).filter(count => count > 0).length / Object.keys(fileCoverage.l).length * 100 : 100,
  };
}

function checkFileCoverage(filePath, coverage) {
  const metrics = calculateCoverage(coverage);
  const failures = [];

  Object.entries(COVERAGE_THRESHOLDS).forEach(([metric, threshold]) => {
    if (metrics[metric] < threshold) {
      failures.push(`${metric}: ${metrics[metric].toFixed(2)}% (required: ${threshold}%)`);
    }
  });

  return { metrics, failures };
}

function main() {
  console.log('🔍 Checking coverage for changed files...\n');

  const changedFiles = getChangedFiles();
  
  if (changedFiles.length === 0) {
    console.log('✅ No changed source files found or could not determine changes.');
    return;
  }

  console.log(`📁 Found ${changedFiles.length} changed file(s):`);
  changedFiles.forEach(file => console.log(`   - ${file}`));
  console.log();

  const coverageData = parseCoverageReport();
  let hasFailures = false;

  changedFiles.forEach(file => {
    const fullPath = path.resolve(file);
    const coverage = coverageData[fullPath];

    if (!coverage) {
      console.log(`⚠️  ${file}: No coverage data found (file may not be tested)`);
      hasFailures = true;
      return;
    }

    const { metrics, failures } = checkFileCoverage(file, coverage);

    if (failures.length > 0) {
      console.log(`❌ ${file}:`);
      failures.forEach(failure => console.log(`   ${failure}`));
      hasFailures = true;
    } else {
      console.log(`✅ ${file}: All coverage thresholds met`);
      console.log(`   statements: ${metrics.statements.toFixed(2)}%, branches: ${metrics.branches.toFixed(2)}%, functions: ${metrics.functions.toFixed(2)}%, lines: ${metrics.lines.toFixed(2)}%`);
    }
  });

  if (hasFailures) {
    console.log('\n❌ Some files do not meet coverage thresholds.');
    console.log('💡 Add tests for the failing files or use /* istanbul ignore next */ for untestable code.');
    process.exit(1);
  } else {
    console.log('\n✅ All changed files meet coverage requirements!');
  }
}

if (require.main === module) {
  main();
}

module.exports = { getChangedFiles, parseCoverageReport, calculateCoverage, checkFileCoverage };
